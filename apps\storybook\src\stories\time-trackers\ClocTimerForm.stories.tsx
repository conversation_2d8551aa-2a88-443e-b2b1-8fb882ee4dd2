import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { ClocTimerForm } from '@cloc/atoms';

const meta = {
	title: 'Time Trackers/Timer Form',
	component: ClocTimerForm,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component:
					'A comprehensive timer form component that includes selectors for client, project, team, and task. Essential for setting up time tracking context before starting a timer.'
			}
		}
	},
	argTypes: {
		size: {
			control: 'select',
			options: ['default', 'sm', 'lg'],
			description: 'Size variant for all form controls'
		}
	}
} satisfies Meta<typeof ClocTimerForm>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		size: 'default'
	}
};

export const Small: Story = {
	args: {
		size: 'sm'
	}
};

export const Large: Story = {
	args: {
		size: 'lg'
	}
};

export const InTimerWidget: Story = {
	render: () => (
		<div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-md">
			<h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Start Time Tracking</h2>
			<ClocTimerForm size="default" />
			<div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
				<button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
					Start Timer
				</button>
			</div>
		</div>
	)
};

export const InSidebar: Story = {
	render: () => (
		<div className="w-80 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 p-4">
			<div className="space-y-6">
				<div>
					<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Quick Timer Setup</h3>
					<ClocTimerForm size="sm" />
				</div>
				<div className="space-y-2">
					<button className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded text-sm transition-colors">
						▶ Start Timer
					</button>
					<button className="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded text-sm transition-colors">
						⏸ Pause Timer
					</button>
				</div>
			</div>
		</div>
	)
};

export const CompactLayout: Story = {
	render: () => (
		<div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
			<div className="flex items-center gap-4">
				<div className="flex-1">
					<ClocTimerForm size="sm" />
				</div>
				<button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium whitespace-nowrap">
					Start
				</button>
			</div>
		</div>
	)
};

export const WithTimer: Story = {
	render: () => (
		<div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 max-w-lg">
			<div className="text-center mb-6">
				<div className="text-4xl font-mono font-bold text-gray-900 dark:text-white mb-2">00:00:00</div>
				<p className="text-gray-500 dark:text-gray-400">Ready to start tracking</p>
			</div>

			<div className="space-y-4">
				<ClocTimerForm size="default" />

				<div className="flex gap-3">
					<button className="flex-1 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
						▶ Start
					</button>
					<button className="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors">
						⏸ Pause
					</button>
					<button className="flex-1 bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
						⏹ Stop
					</button>
				</div>
			</div>
		</div>
	)
};

export const MobileView: Story = {
	render: () => (
		<div className="w-full max-w-sm mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-lg">
			<div className="p-4 border-b border-gray-200 dark:border-gray-700">
				<h2 className="text-lg font-semibold text-gray-900 dark:text-white text-center">Time Tracker</h2>
			</div>
			<div className="p-4 space-y-4">
				<ClocTimerForm size="default" />
				<button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
					Start Tracking
				</button>
			</div>
		</div>
	)
};

export const QuickStart: Story = {
	render: () => (
		<div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 p-6 rounded-lg">
			<div className="max-w-md mx-auto">
				<div className="text-center mb-6">
					<h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Quick Start</h2>
					<p className="text-gray-600 dark:text-gray-400">Set up your timer in seconds</p>
				</div>

				<div className="bg-white dark:bg-gray-900 rounded-lg p-4 shadow-sm">
					<ClocTimerForm size="default" />
					<div className="mt-4 flex gap-2">
						<button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition-colors">
							Start Now
						</button>
						<button className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
							Save as Template
						</button>
					</div>
				</div>
			</div>
		</div>
	)
};

export const WithPresets: Story = {
	render: () => (
		<div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 max-w-lg">
			<h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Timer Setup</h2>

			<div className="space-y-4">
				<div>
					<h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quick Presets</h3>
					<div className="grid grid-cols-2 gap-2">
						<button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">
							Development Work
						</button>
						<button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">
							Client Meeting
						</button>
						<button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">
							Code Review
						</button>
						<button className="p-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors">
							Documentation
						</button>
					</div>
				</div>

				<div className="border-t border-gray-200 dark:border-gray-700 pt-4">
					<h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Custom Setup</h3>
					<ClocTimerForm size="default" />
				</div>

				<button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
					Start Timer
				</button>
			</div>
		</div>
	)
};
