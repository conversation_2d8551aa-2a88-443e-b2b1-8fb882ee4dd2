import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ClocReportDatesRangePicker } from '@cloc/atoms';

/**
 * ClocReportDatesRangePicker provides global date range selection for reports.
 * 
 * ## Features
 * 
 * - **Date Range Selection**: Interactive calendar-based date range picker
 * - **Global State**: Updates reportDates in global context for all reports
 * - **Dual Calendar**: Two-month calendar view for easy range selection
 * - **Visual Feedback**: Clear start/end date indicators with custom styling
 * - **Theme Integration**: Automatically adapts to current theme colors
 * - **Responsive Design**: Adaptive layout for different screen sizes
 * - **Date Formatting**: Human-readable date format display
 * 
 * ## Data Source
 * 
 * This component gets its data from the `useClocContext()` hook, which provides:
 * - Current reportDates state from global context
 * - setReportDates function for updating global state
 * - Applied theme for visual customization
 * 
 * ## Use Cases
 * 
 * - **Report Generation**: Setting date ranges for all types of reports
 * - **Analytics Filtering**: Filtering analytics data by date range
 * - **Time Period Analysis**: Selecting specific time periods for analysis
 * - **Dashboard Context**: Setting global date context for dashboard widgets
 * - **Data Export**: Defining date ranges for data export operations
 */
const meta: Meta<typeof ClocReportDatesRangePicker> = {
	title: 'Inputs/Global Selectors/Cloc Report Dates Range Picker',
	component: ClocReportDatesRangePicker,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component: `
ClocReportDatesRangePicker is a specialized global date range selector that provides comprehensive date range selection capabilities for report generation and data filtering. It wraps the ClocDateRangePicker component with global state management for report dates.

### Key Capabilities

- **Global Date Management**: Seamlessly integrates with global reportDates state for consistent date ranges across all reports
- **Interactive Calendar**: Dual-month calendar interface for intuitive date range selection
- **Theme Integration**: Automatically adapts visual styling to match the current application theme
- **Visual Indicators**: Clear start and end date highlighting with custom colors and styling
- **Date Formatting**: Human-readable date format display (e.g., "Jan 01, 2024 - Jan 07, 2024")
- **Responsive Layout**: Adaptive design that works across different screen sizes and contexts

### Technical Implementation

The component serves as a wrapper around ClocDateRangePicker, connecting it to the global reportDates state from ClocProvider context. It inherits all the functionality of the underlying date range picker while providing seamless integration with the global reporting system.

### Integration with Reports

This component is designed to work seamlessly with all report components in the system, providing a consistent date range selection experience that automatically updates all connected reports and analytics when the date range changes.
				`
			}
		}
	},
	argTypes: {},
	decorators: [
		(Story) => (
			<div style={{ width: '400px', height: '300px' }}>
				<Story />
			</div>
		)
	]
};

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default report dates range picker with full functionality.
 * Shows the complete date range selection interface for reports.
 */
export const Default: Story = {
	args: {},
	parameters: {
		docs: {
			description: {
				story: 'The default ClocReportDatesRangePicker component with full date range selection functionality. Displays an interactive calendar interface for selecting date ranges that automatically updates the global reportDates state for all connected reports and analytics.'
			}
		}
	}
};

/**
 * Report dates range picker demonstrating the selection interface.
 * Shows how the component appears during date range selection.
 */
export const CustomStyling: Story = {
	args: {},
	parameters: {
		docs: {
			description: {
				story: 'ClocReportDatesRangePicker demonstrating the date range selection interface. The component automatically inherits theme colors and styling from the ClocProvider context, providing consistent visual integration with the application theme.'
			}
		}
	}
};
