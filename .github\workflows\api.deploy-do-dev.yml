name: Deploy API to DigitalOcean Dev

on:
  push:
    branches: [develop]

concurrency:
  group: ${{ github.ref }}-${{ github.workflow }}
  cancel-in-progress: true

jobs:
  deploy-api-dev:
    runs-on: buildjet-4vcpu-ubuntu-2204
    environment: dev

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Log in to DigitalOcean Container Registry with short-lived credentials
        run: doctl registry login --expiry-seconds 600

      - name: Save DigitalOcean kubeconfig with short-lived credentials
        run: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 k8s-gauzy

      - name: Generate TLS Secrets for API Ingress
        run: |
          rm -f ${HOME}/ingress.api.crt ${HOME}/ingress.api.key
          echo ${{ secrets.INGRESS_CERT }} | base64 --decode > ${HOME}/ingress.api.crt
          echo ${{ secrets.INGRESS_CERT_KEY }} | base64 --decode > ${HOME}/ingress.api.key
          kubectl create secret tls api.cloc.co-tls --save-config --dry-run=client --cert=${HOME}/ingress.api.crt --key=${HOME}/ingress.api.key -o yaml | kubectl --context do-sfo2-k8s-gauzy apply -f -

      - name: Apply k8s manifests changes in DigitalOcean k8s cluster (if any)
        run: |
          envsubst < $GITHUB_WORKSPACE/.deploy/k8s/k8s-manifest-api.dev.yaml | kubectl --context do-sfo2-k8s-gauzy apply -f -
        env:
          # below we are using GitHub secrets for both frontend and backend
          CLOUD_PROVIDER: 'DO'
          DB_NAME: '${{ secrets.DB_NAME }}'
          SENTRY_DSN: '${{ secrets.SENTRY_DSN }}'
          SENTRY_TRACES_SAMPLE_RATE: '${{ secrets.SENTRY_TRACES_SAMPLE_RATE }}'
          SENTRY_PROFILE_SAMPLE_RATE: '${{ secrets.SENTRY_PROFILE_SAMPLE_RATE }}'
          SENTRY_HTTP_TRACING_ENABLED: '${{ secrets.SENTRY_HTTP_TRACING_ENABLED }}'
          SENTRY_POSTGRES_TRACKING_ENABLED: '${{ secrets.SENTRY_POSTGRES_TRACKING_ENABLED }}'
          SENTRY_PROFILING_ENABLED: '${{ secrets.SENTRY_PROFILING_ENABLED }}'
          OTEL_ENABLED: '${{ secrets.OTEL_ENABLED }}'
          OTEL_PROVIDER: '${{ secrets.OTEL_PROVIDER }}'
          OTEL_EXPORTER_OTLP_TRACES_ENDPOINT: '${{ secrets.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT }}'
          OTEL_EXPORTER_OTLP_HEADERS: '${{ secrets.OTEL_EXPORTER_OTLP_HEADERS }}'
          FEATURE_OPEN_STATS: '${{ vars.FEATURE_OPEN_STATS }}'

      - name: Restart Pods to pick up :latest tag version
        run: |
          kubectl --context do-sfo2-k8s-gauzy rollout restart deployment/ever-cloc-dev-api
