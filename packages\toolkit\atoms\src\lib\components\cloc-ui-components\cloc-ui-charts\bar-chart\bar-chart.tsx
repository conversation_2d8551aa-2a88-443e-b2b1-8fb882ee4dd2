'use client';

import { <PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CartesianGrid, LabelList, XAxis, Y<PERSON><PERSON><PERSON> } from 'recharts';
import { ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent, ChartCustomLabel } from '../chart';
import React from 'react';
import { IChartProps } from '@cloc/types';
import { ChartContainer } from '../chart-container';

import { useClocContext } from '@lib/context/cloc-context';
import { generateRandomColors } from '@cloc/ui';

const BarChart: React.FC<IChartProps> = ({
	data = [],
	config,
	title,
	description,
	layout = 'horizontal',
	footer,

	color = 'black'
}) => {
	const { config: contextConfig } = useClocContext();
	const finalConfig = config || contextConfig;

	// Ensure that data[0] exists before trying to access its keys
	const hasData = data.length > 0;
	const colors = color && hasData ? generateRandomColors(color, Object.keys(data[0]).length) : '';

	return (
		<ChartContainer title={title} description={description} footer={footer} config={finalConfig}>
			<RechartBarChart
				accessibilityLayer
				data={data}
				layout={layout === 'vertical' ? 'vertical' : 'horizontal'}
				margin={layout === 'vertical' ? { left: -20 } : {}}
			>
				{layout === 'vertical' ? (
					<>
						<CartesianGrid vertical={true} horizontal={false} />
						<XAxis type="number" dataKey={hasData ? Object.keys(data[0])[3] : ''} hide={false} />

						<YAxis
							dataKey={hasData ? Object.keys(data[0])[0] : ''}
							tickLine={false}
							type="category"
							tickMargin={10}
							axisLine={false}
							tickFormatter={(value) => value.split('-')[2] + '/' + value.split('-')[1]}
							// tickFormatter={(value) => value.slice(0, 3)}
						/>
					</>
				) : (
					<>
						<CartesianGrid vertical={false} />

						<XAxis
							dataKey={hasData ? Object.keys(data[0])[0] : ''}
							tickLine={false}
							tickMargin={10}
							axisLine={false}
							tickFormatter={(value) => value.split('-')[2] + '/' + value.split('-')[1]}
							// tickFormatter={(value) => value.slice(0, 3)}
						/>
					</>
				)}

				<ChartLegend
					className="w-full flex flex-wrap justify-center items-center"
					content={<ChartLegendContent />}
				/>
				<ChartTooltip cursor={true} content={<ChartTooltipContent hideLabel={true} indicator={'dot'} />} />
				{/* Render bars only if data[0] has more than 1 key */}

				{hasData &&
					Object.keys(data[0]).map((item, index) => {
						return (
							index !== 0 && (
								<Bar key={index} dataKey={item} fill={colors[index - 1]} radius={15}>
									{layout !== 'vertical' && (
										<LabelList
											position="centerTop"
											offset={12}
											className="fill-[hsl(var(--foreground))]"
											fontSize={12}
											content={<ChartCustomLabel width={3} x={3} y={5} />}
										/>
									)}
								</Bar>
							)
						);
					})}
			</RechartBarChart>
		</ChartContainer>
	);
};

export { BarChart };
