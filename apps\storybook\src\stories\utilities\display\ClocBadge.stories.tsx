import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { ClocBadge } from '@cloc/atoms';

const meta = {
	title: 'Utilities/Display/Badge',
	component: ClocBadge,
	parameters: {
		layout: 'centered'
	},
	argTypes: {
		variant: {
			control: 'select',
			options: ['default', 'secondary', 'destructive', 'outline'],
			description: 'The visual style variant of the badge'
		},
		children: {
			control: 'text',
			description: 'The content to display inside the badge'
		},
		className: {
			control: 'text',
			description: 'Additional CSS classes to apply'
		}
	}
} satisfies Meta<typeof ClocBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		children: 'Badge',
		variant: 'default'
	}
};

export const Secondary: Story = {
	args: {
		children: 'Secondary',
		variant: 'secondary'
	}
};

export const Destructive: Story = {
	args: {
		children: 'Destructive',
		variant: 'destructive'
	}
};

export const Outline: Story = {
	args: {
		children: 'Outline',
		variant: 'outline'
	}
};

export const WithNumbers: Story = {
	args: {
		children: '42',
		variant: 'default'
	}
};

export const LongText: Story = {
	args: {
		children: 'Very Long Badge Text',
		variant: 'secondary'
	}
};

export const StatusBadges: Story = {
	render: () => (
		<div className="flex gap-2 flex-wrap">
			<ClocBadge variant="default">Active</ClocBadge>
			<ClocBadge variant="secondary">Pending</ClocBadge>
			<ClocBadge variant="destructive">Error</ClocBadge>
			<ClocBadge variant="outline">Draft</ClocBadge>
		</div>
	)
};

export const CounterBadges: Story = {
	render: () => (
		<div className="flex gap-2 flex-wrap">
			<ClocBadge variant="default">1</ClocBadge>
			<ClocBadge variant="secondary">99+</ClocBadge>
			<ClocBadge variant="destructive">!</ClocBadge>
			<ClocBadge variant="outline">NEW</ClocBadge>
		</div>
	)
};

export const CustomStyling: Story = {
	args: {
		children: 'Custom',
		variant: 'default',
		className: 'bg-blue-500 text-white border-blue-600'
	}
};

export const AllVariants: Story = {
	render: () => (
		<div className="grid grid-cols-2 gap-4">
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Light Theme</h3>
				<div className="flex gap-2 flex-wrap">
					<ClocBadge variant="default">Default</ClocBadge>
					<ClocBadge variant="secondary">Secondary</ClocBadge>
					<ClocBadge variant="destructive">Destructive</ClocBadge>
					<ClocBadge variant="outline">Outline</ClocBadge>
				</div>
			</div>
		</div>
	)
};
