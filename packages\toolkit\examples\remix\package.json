{"name": "@cloc/example-remix", "private": true, "sideEffects": false, "version": "0.1.0", "type": "module", "scripts": {"dev": "remix dev", "build": "remix build", "start": "remix-serve build/index.js", "typecheck": "tsc", "lint": "eslint . --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write ."}, "dependencies": {"@remix-run/node": "latest", "@remix-run/react": "latest", "@remix-run/serve": "latest", "@cloc/atoms": "*", "isbot": "^5.1.25", "jotai": "^2.11.0", "react": "^19.1.0", "react-day-picker": "^9.5.0", "react-dom": "^19.1.0", "theme-ui": "^0.17.1"}, "devDependencies": {"@remix-run/dev": "^2.15.2", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "typescript": "^5.1.6", "vite": "^5.4.21", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}