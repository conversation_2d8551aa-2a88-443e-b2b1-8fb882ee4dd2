import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { ClocProgressCircle } from '@cloc/atoms';

const meta = {
	title: 'Utilities/Display/Progress Circle',
	component: ClocProgressCircle,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component:
					"A circular progress indicator that automatically calculates progress based on today's tracked time from the Cloc context. Shows progress towards an 8-hour daily goal with customizable appearance."
			}
		}
	},
	argTypes: {
		percentage: {
			control: { type: 'range', min: 0, max: 100, step: 1 },
			description: 'Progress percentage (0-100). Auto-calculated from context if not provided.'
		},
		radius: {
			control: { type: 'range', min: 20, max: 100, step: 5 },
			description: 'Radius of the progress circle'
		},
		strokeWidth: {
			control: { type: 'range', min: 2, max: 20, step: 1 },
			description: 'Width of the progress stroke'
		},
		size: {
			control: { type: 'range', min: 40, max: 200, step: 10 },
			description: 'Overall size of the component'
		},
		duration: {
			control: { type: 'range', min: 100, max: 2000, step: 100 },
			description: 'Animation duration in milliseconds'
		},
		colors: {
			control: 'object',
			description: 'Custom color scheme for the progress circle'
		}
	}
} satisfies Meta<typeof ClocProgressCircle>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {}
};

export const CustomPercentage: Story = {
	args: {
		percentage: 75
	}
};

export const SmallSize: Story = {
	args: {
		size: 60,
		radius: 25,
		strokeWidth: 6
	}
};

export const LargeSize: Story = {
	args: {
		size: 120,
		radius: 55,
		strokeWidth: 12
	}
};

export const ThinStroke: Story = {
	args: {
		strokeWidth: 4,
		percentage: 60
	}
};

export const ThickStroke: Story = {
	args: {
		strokeWidth: 16,
		percentage: 80
	}
};

export const FastAnimation: Story = {
	args: {
		duration: 200,
		percentage: 90
	}
};

export const SlowAnimation: Story = {
	args: {
		duration: 1500,
		percentage: 45
	}
};

export const CustomColors: Story = {
	args: {
		percentage: 65,
		colors: {
			primary: '#10b981',
			secondary: '#34d399',
			background: '#d1fae5'
		}
	}
};

export const RedTheme: Story = {
	args: {
		percentage: 85,
		colors: {
			primary: '#ef4444',
			secondary: '#f87171',
			background: '#fecaca'
		}
	}
};

export const BlueTheme: Story = {
	args: {
		percentage: 50,
		colors: {
			primary: '#3b82f6',
			secondary: '#60a5fa',
			background: '#bfdbfe'
		}
	}
};

export const ProgressStates: Story = {
	render: () => (
		<div className="grid grid-cols-2 md:grid-cols-4 gap-6">
			<div className="text-center space-y-2">
				<ClocProgressCircle percentage={0} />
				<p className="text-sm text-gray-600">Not Started</p>
			</div>
			<div className="text-center space-y-2">
				<ClocProgressCircle percentage={25} />
				<p className="text-sm text-gray-600">Quarter Done</p>
			</div>
			<div className="text-center space-y-2">
				<ClocProgressCircle percentage={50} />
				<p className="text-sm text-gray-600">Half Way</p>
			</div>
			<div className="text-center space-y-2">
				<ClocProgressCircle percentage={100} />
				<p className="text-sm text-gray-600">Complete</p>
			</div>
		</div>
	)
};

export const SizeVariations: Story = {
	render: () => (
		<div className="flex items-center gap-6">
			<div className="text-center space-y-2">
				<ClocProgressCircle size={40} radius={18} strokeWidth={4} percentage={75} />
				<p className="text-xs text-gray-600">Small</p>
			</div>
			<div className="text-center space-y-2">
				<ClocProgressCircle size={60} radius={25} strokeWidth={6} percentage={75} />
				<p className="text-xs text-gray-600">Medium</p>
			</div>
			<div className="text-center space-y-2">
				<ClocProgressCircle size={80} radius={35} strokeWidth={8} percentage={75} />
				<p className="text-xs text-gray-600">Large</p>
			</div>
			<div className="text-center space-y-2">
				<ClocProgressCircle size={100} radius={45} strokeWidth={10} percentage={75} />
				<p className="text-xs text-gray-600">Extra Large</p>
			</div>
		</div>
	)
};
