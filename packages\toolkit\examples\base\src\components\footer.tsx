'use client';
import { FC } from 'react';
import { ThemeToggle } from '@cloc/atoms';
import Link from 'next/link';
import { C<PERSON><PERSON>ogo, ClocLogoIcon } from '@cloc/ui';

const FooterLinks: {
	href: string;
	label: string;
	external?: boolean;
}[] = [
	{ href: 'https://ever.co/about-us', label: 'About Us', external: true },
	{ href: 'https://blog.cloc.ai/', label: 'Blog', external: true },
	{ href: 'https://cloc.ai/tos', label: 'Terms Of Service', external: true },
	{ href: 'https://cloc.ai/privacy', label: 'Privacy Policy', external: true },
	{ href: 'https://cloc.ai/cookies', label: 'Cookie Policy', external: true },
	{ href: 'https://cloc.ai/delete', label: 'Delete Data', external: true }
];
export const Footer: FC = () => {
	return (
		<footer className="border-b border-slate-200 dark:border-slate-800 bg-slate-100 dark:bg-slate-950 relative overflow-hidden w-full  border-t  p-5 mt-6 sm:flex sm:items-center gap-5 gap-y-8 md:gap-y-5 gap-x-3 sm:justify-between">
			<div className="flex items-center gap-4 text-sm">
				{FooterLinks.map((elt, index) => {
					return (
						<Link
							key={index}
							className="hover:underline inline-flex gap-x-2 capitalize text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
							href={elt.href}
							target={elt.external ? '_blank' : ''}
						>
							{elt.label}
						</Link>
					);
				})}
			</div>

			<div className="flex gap-4 text-sm  capitalize text-gray-600  dark:text-gray-400 ">
				<p>Copyright © 2024-present</p>
				<Link className="hover:underline hover:text-white" href={'https://ever.co/'}>
					Ever Co. LTD.
				</Link>
				<p>All Rights Reserved</p>
			</div>

			<div className="space-x-4 flex items-center justify-between">
				<ThemeToggle />
			</div>
		</footer>
	);
};
