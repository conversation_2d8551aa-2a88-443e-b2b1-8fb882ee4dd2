import { Meta } from '@storybook/blocks';
import '../../../.storybook/style.css';

<Meta
  title="Introduction/Welcome to Ever Cloc SDK"
/>

<img
  src="./bg-cover-light.webp"
  alt="Ever Cloc SDK Cover"
  className="w-full rounded-lg shadow-lg mb-8"
/>

# Welcome to Ever Cloc SDK

##  What is Ever Cloc?

Ever Cloc is a powerful, self-hosted and cloud-hosted time tracking software that provides developers with a complete SDK for building time tracking applications. Our platform combines beautiful UI components, advanced analytics, and seamless integrations to help you create professional time tracking solutions.

##  Key Features

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 my-8">
  <div className="p-6 border border-slate-200 rounded-lg bg-slate-50 ">
    <h3 className="text-slate-900  font-semibold mb-4">⏱️ Time Tracking Components</h3>
    <p className="text-slate-600 ">
      Multiple timer styles including Modern, Basic, and Custom variants with progress indicators, themes, and interactive controls.
    </p>
  </div>

  <div className="p-6 border border-slate-200 rounded-lg bg-slate-50 ">
    <h3 className="text-slate-900  font-semibold mb-4">📊 Analytics & Tracking</h3>
    <p className="text-slate-600 ">
      Real-time user interaction tracking, session replay, heatmap visualization, and detailed analytics insights powered by Microsoft Clarity.
    </p>
  </div>

  <div className="p-6 border border-slate-200 rounded-lg bg-slate-50 ">
    <h3 className="text-slate-900  font-semibold mb-4">📈 Reports & Charts</h3>
    <p className="text-slate-600 ">
      Comprehensive reporting with working hours visualization, project/task reports, and customizable chart components.
    </p>
  </div>

  <div className="p-6 border border-slate-200 rounded-lg bg-slate-50 ">
    <h3 className="text-slate-900  font-semibold mb-4">🎨 UI Component Library</h3>
    <p className="text-slate-600 ">
      Built on Radix UI and Tailwind CSS with buttons, forms, date pickers, charts, and more - all fully customizable.
    </p>
  </div>

  <div className="p-6 border border-slate-200 rounded-lg bg-slate-50 ">
    <h3 className="text-slate-900  font-semibold mb-4">🔐 Authentication</h3>
    <p className="text-slate-600 ">
      Complete authentication system with login forms, user management, and secure token handling.
    </p>
  </div>

  <div className="p-6 border border-slate-200 rounded-lg bg-slate-50 ">
    <h3 className="text-slate-900  font-semibold mb-4">🌍 Internationalization</h3>
    <p className="text-slate-600 ">
      Multi-language support with react-i18next integration and comprehensive localization features.
    </p>
  </div>
</div>


## SDK Architecture

Our SDK is organized into focused packages, each serving specific purposes:

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 my-8">
  <div className="p-4 border border-slate-200  rounded-md bg-white ">
    <h4 className="text-slate-900  font-medium mb-2">@cloc/atoms</h4>
    <p className="text-sm text-slate-600 ">
      Main component library with time trackers, forms, reports, and tracking components
    </p>
  </div>

  <div className="p-4 border border-slate-200  rounded-md bg-white ">
    <h4 className="text-slate-900  font-medium mb-2">@cloc/ui</h4>
    <p className="text-sm text-slate-600 ">
      Base UI components built on Radix UI and Tailwind CSS
    </p>
  </div>

  <div className="p-4 border border-slate-200  rounded-md bg-white ">
    <h4 className="text-slate-900  font-medium mb-2">@cloc/api</h4>
    <p className="text-sm text-slate-600 ">
      API client functionality for backend integration
    </p>
  </div>

  <div className="p-4 border border-slate-200  rounded-md bg-white ">
    <h4 className="text-slate-900  font-medium mb-2">@cloc/types</h4>
    <p className="text-sm text-slate-600 ">
      TypeScript definitions and interfaces
    </p>
  </div>

  <div className="p-4 border border-slate-200  rounded-md bg-white ">
    <h4 className="text-slate-900  font-medium mb-2">@cloc/tracking</h4>
    <p className="text-sm text-slate-600 ">
      Analytics and tracking system with Microsoft Clarity integration
    </p>
  </div>
</div>

## Quick Start

Get started with Ever Cloc SDK in just a few steps:

```bash
# Install the core packages
npm install @cloc/atoms @cloc/ui @cloc/api @cloc/types

# Optional: Install tracking for analytics
npm install @cloc/tracking
```

```jsx
import { ClocProvider, ModernCloc, BasicTimer } from '@cloc/atoms';
import { Button } from '@cloc/ui';

function App() {
  return (
    <ClocProvider config={{ apiUrl: 'https://api.your-domain.com' }}>
      <div className="p-4">
        <h1>My Time Tracking App</h1>
        <ModernCloc showProgress={true} />
        <BasicTimer />
        <Button>Get Started</Button>
      </div>
    </ClocProvider>
  );
}
```

## Explore More

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 my-8">

  <div className="p-6 border-2 border-amber-500 rounded-lg text-center bg-white ">
    <h3 className="text-amber-500 font-semibold mb-4">📚 Getting Started</h3>
    <p className="text-sm text-slate-600  mb-4">
      Learn how to install and configure the SDK
    </p>
    <a href="https://docs.cloc.ai/docs/introduction/installation-guide" target="_blank" className="text-amber-500 no-underline font-bold hover:underline">
      Read Guide →
    </a>
  </div>
  <div className="p-6 border-2 border-blue-500 rounded-lg text-center bg-white ">
    <h3 className="text-blue-500 font-semibold mb-4">⏰ Timer Components</h3>
    <p className="text-sm text-slate-600  mb-4">
      Explore our comprehensive collection of timer components
    </p>
    <a href="/?path=/story/time-trackers-modern-cloc--small" className="text-blue-500 no-underline font-bold hover:underline">
      View Timer Stories →
    </a>
  </div>

  <div className="p-6 border-2 border-emerald-500 rounded-lg text-center bg-white ">
    <h3 className="text-emerald-500 font-semibold mb-4">🎨 UI Components</h3>
    <p className="text-sm text-slate-600  mb-4">
      Discover our complete UI component library
    </p>
    <a href="/?path=/story/utilities-buttons-cloc-button--small-cloc-button" className="text-emerald-500 no-underline font-bold hover:underline">
      View UI Stories →
    </a>
  </div>


</div>

## Resources

- **<a href="https://cloc.ai" target="_blank">Official Website</a>** - Learn more about Ever Cloc Platform
- **<a href="https://docs.cloc.ai" target="_blank">Documentation</a>** - Comprehensive platform documentation
- **<a href="https://github.com/cloc-co/ever-cloc" target="_blank">GitHub Repository</a>** - Source code and contributions
- **<a href="https://app.cloc.ai" target="_blank">Demo Application</a>** - Try the platform live
- **<a href="https://builders.cloc.ai" target="_blank">Visual Builders</a>** - No-code component builders

---

<div className="text-center p-8 bg-slate-100  rounded-lg my-8">
  <p className="text-slate-600 ">
    Built with ❤️ by the Ever Co. team • Licensed under AGPL-3.0
  </p>
</div>
