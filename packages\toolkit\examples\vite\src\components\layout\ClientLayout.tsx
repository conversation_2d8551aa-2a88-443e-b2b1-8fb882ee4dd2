import { Cloc<PERSON>rovider, theme8, useClocContext } from '@cloc/atoms';
import NavBar from './NavBar';
import { ErrorBoundary } from 'react-error-boundary';
import { Footer } from './Footer';
import { useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import G<PERSON>ientBackground from './GradientBackground';
import { tracker } from '@cloc/tracking';

function ErrorFallback({ error }: { error: Error }) {
	const isDev = import.meta.env.DEV;
	return (
		<div className="flex min-h-screen items-center justify-center">
			<div className="text-center">
				<h2 className="text-lg font-semibold">Something went wrong</h2>
				<p className="mt-2 text-sm text-gray-500">
					{isDev ? error.message : 'Please try refreshing the page or contact support.'}
				</p>
			</div>
		</div>
	);
}

const clocConfig = {
	apiUrl: import.meta.env.VITE_CLOC_API_URL
};

export default function ClientLayout() {
	return (
		<ClocProvider config={clocConfig} theme={theme8}>
			<ClientLayoutContent />
		</ClocProvider>
	);
}

function ClientLayoutContent() {
	const { authenticatedUser: user, token, selectedOrganization: organizationId } = useClocContext();

	useEffect(() => {
		if (user && token && organizationId) {
			try {
				tracker.start({
					organizationId,
					tenantId: user.tenantId,
					token
				});
			} catch (error) {
				console.error('Failed to start tracker:', error);
			}
			return () => {
				try {
					tracker.stop();
				} catch (error) {
					console.error('Failed to stop tracker:', error);
				}
			};
		}
	}, [user, token, organizationId]);

	return (
		<ErrorBoundary FallbackComponent={ErrorFallback}>
			<GradientBackground />
			<div
				data-clarity-unmask="true"
				className="relative min-h-screen text-black dark:text-white font-[family-name:var(--font-geist-sans)] tracking-[-0.020em] flex flex-col justify-center items-center  mx-auto py-8 px-4"
			>
				<NavBar />
				<main className="tracking-tight">
					<div>
						<Outlet />
					</div>
				</main>
				<Footer />
			</div>
		</ErrorBoundary>
	);
}
