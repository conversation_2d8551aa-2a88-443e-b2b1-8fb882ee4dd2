import { useEffect, useState, ReactNode } from 'react';
import { Theme, ThemeUIProvider } from 'theme-ui';

import { Provider as Jo<PERSON>Provider } from 'jotai';
import { useAtom } from 'jotai';

import { ChartConfig, ChartData } from '@cloc/types';
import { useAccessToken } from '@hooks/useAccessToken';
import { useAuthUser } from '@hooks/useAuthUser';
import { useTimerStatus } from '@hooks/useTimerStatus';
import { useTimer } from '@hooks/useTimer';
import { useReport } from '@hooks/useReport';
import { useOrganizationTeams } from '@hooks/useOrganisationTeams';
import { useMyTasks } from '@hooks/useMyTasks';
import { useOrganizationProjects } from '@hooks/useOrganisationProjects';
import {
	appliedThemeAtom,
	currentClocStateAtom,
	reportDatesAtom,
	selectedEmployeeAtom,
	selectedTeamAtom
} from '@lib/cloc-jotai/atoms/cloc-atoms';
import useFontSelector from '@hooks/useFontSelector';
import { useEmployeeOrganization } from '@hooks/useEmployeeOrganisation';
import { Toaster } from '@cloc/ui';
import { useUserPermission } from '@hooks/useUserPermission';
import { useMember } from '@hooks/useMember';
import { ThemeProvider } from 'next-themes';
import { useStatisticsCounts } from '@hooks/useStatisticsCounts';
import { useProjectsStats } from '@hooks/useProjectsStatistics';
import { useTasksStats } from '@hooks/useTasksStatistics';
import { useActivitiesStats } from '@hooks/useActivitiesStatistics';
import { useUserOrganization } from '@hooks/useUserOrganization';
import { I18nextProvider } from 'react-i18next';
import { changeClocLanguage } from '@components/i18n/language-switch';
import { ClocContext } from '@lib/context/cloc-context';
import i18n from '@lib/i18n/init';
import { useSelectedOrganization } from '@hooks/useSelectedOrganization';
import { apiConfigManager, IApiClocConfig } from '@cloc/api';
import TrackingProvider from '@lib/context/cloc-tracking-context';

export const defaultData: ChartData[] = [
	{ day: 'Monday', cedric: 5, salva: 7, josh: 2, ndeko: 7 },
	{ day: 'Tuesday', cedric: 8, salva: 4, josh: 7, ndeko: 2 },
	{ day: 'Wednesday', cedric: 8, salva: 10, josh: 2, ndeko: 3 },
	{ day: 'Thursday', cedric: 5, salva: 11, josh: 9, ndeko: 1 },
	{ day: 'Friday', cedric: 13, salva: 5, josh: 13, ndeko: 8 },
	{ day: 'Saturday', cedric: 4, salva: 7, josh: 5, ndeko: 3 },
	{ day: 'Sunday', cedric: 4, salva: 7, josh: 6, ndeko: 7 }
];

interface ClocProviderProps {
	children?: ReactNode;
	theme?: Theme<{}>;
	token?: string;
	lang?: string;
	config?: Partial<IApiClocConfig>;
}

const ClocDataProvider = ({ children, config: apiConfig, theme, lang = 'en' }: ClocProviderProps) => {
	// React States
	const [chartConfig, setChartConfig] = useState<ChartConfig>({});
	const [language, setLanguage] = useState(lang);

	// Jotai Atoms
	const [currentClocState, setCurrentClocState] = useAtom(currentClocStateAtom);
	const [reportDates] = useAtom(reportDatesAtom);
	const [appliedTheme, setAppliedTheme] = useAtom(appliedThemeAtom);
	const [selectedEmployee] = useAtom(selectedEmployeeAtom);
	const [selectedTeam] = useAtom(selectedTeamAtom);

	// Custom hooks

	const { fontOptions, selectedFont, setSelectedFont } = useFontSelector();

	const { accessToken } = useAccessToken();

	const { data: user } = useAuthUser(accessToken);

	const { data: userPermissions } = useUserPermission(user, accessToken);

	const { data: userOrganizations } = useUserOrganization(user, accessToken);

	const organizationId = useSelectedOrganization(user, userOrganizations);

	const { data: timerStatus } = useTimerStatus(user, accessToken, organizationId);

	const {
		start,
		pause,
		isRunning,
		hours,
		totalSeconds,
		minutes,
		seconds,
		startTimer,
		stopTimer,
		setTodayTrackedTime,
		todayTrackedTime
	} = useTimer({
		currentClocState,
		token: accessToken,
		timerStatus: timerStatus ?? { duration: 0, lastLog: null, running: false },
		user,
		organizationId
	});

	const {} = useMember(user, accessToken, userPermissions, organizationId);

	// Get Reports and Stats

	const {} = useReport(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);

	const {} = useStatisticsCounts(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);

	const {} = useActivitiesStats(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);

	const {} = useProjectsStats(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);

	const {} = useTasksStats(user, accessToken, selectedEmployee, selectedTeam, organizationId, reportDates);

	// Get Timer Data

	const {} = useEmployeeOrganization(user, accessToken, organizationId);

	const {} = useOrganizationProjects(user, accessToken, currentClocState.clientId, organizationId);

	const {} = useOrganizationTeams(user, accessToken, currentClocState.projectId, organizationId);

	const {} = useMyTasks(user, accessToken, currentClocState.projectId, organizationId);

	// Use Effect

	useEffect(() => {
		timerStatus && timerStatus.duration && setTodayTrackedTime(new Date(timerStatus.duration * 1000));
		timerStatus &&
			timerStatus.lastLog &&
			setCurrentClocState({
				taskId: timerStatus.lastLog.taskId,
				projectId: timerStatus.lastLog.projectId,
				organizationTeamId: timerStatus.lastLog.organizationTeamId,
				clientId: timerStatus.lastLog.organizationContactId
			});
	}, [timerStatus]);

	useEffect(() => {
		theme && setAppliedTheme(theme);
	}, [theme]);

	useEffect(() => {
		const storedLanguage = localStorage.getItem('preferred-language');
		if (storedLanguage) {
			setLanguage(storedLanguage);
		}
	}, [lang]);

	useEffect(() => {
		changeClocLanguage(language);
	}, [language]);

	useEffect(() => {
		if (apiConfig) {
			apiConfigManager.setConfig(apiConfig);
		}
	}, [apiConfig]);

	return (
		<ClocContext.Provider
			value={{
				start,
				pause,
				startTimer,
				stopTimer,
				isRunning,
				hours,
				minutes,
				seconds,
				totalSeconds,
				defaultData,
				config: chartConfig,
				setConfig: setChartConfig,
				fontOptions,
				selectedFont,
				setSelectedFont,
				todayTrackedTime
			}}
		>
			<TrackingProvider config={{ baseUrl: apiConfig?.apiUrl }}>
				<I18nextProvider i18n={i18n}>
					<ThemeProvider attribute="class">
						<ThemeUIProvider theme={appliedTheme}>{children}</ThemeUIProvider>
					</ThemeProvider>
				</I18nextProvider>
			</TrackingProvider>
		</ClocContext.Provider>
	);
};

const ClocProvider = ({ children, ...props }: ClocProviderProps) => {
	return (
		<JotaiProvider>
			<ClocDataProvider {...props}>{children}</ClocDataProvider>
			<Toaster />
		</JotaiProvider>
	);
};

ClocProvider.displayName = 'ClocProvider';

export { ClocProvider };
