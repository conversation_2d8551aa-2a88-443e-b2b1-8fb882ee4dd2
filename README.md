# Ever Cloc Platform

[uri_cloc]: https://cloc.ai
[uri_license]: https://www.gnu.org/licenses/agpl-3.0.html
[uri_license_image]: https://img.shields.io/badge/License-AGPL%20v3-blue.svg

![visitors](https://visitor-badge.laobi.icu/badge?page_id=cloc-co.ever-cloc-platform)
[![License: AGPL v3][uri_license_image]][uri_license]
[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/cloc-co/ever-cloc)
[![Gitpod Ready-to-Code](https://img.shields.io/badge/Gitpod-Ready--to--Code-blue?logo=gitpod)](https://gitpod.io/#https://github.com/cloc-co/ever-cloc)

## 🌟 What is it

[Ever® Cloc™](https://cloc.ai) - Self-hosted and Cloud-hosted Time Tracking Software.

**NOTE: Platform currently is in active development stage / WIP, please use with caution!**

## ✨ Features

TODO

## 🌼 Screenshots

TODO

## 📊 Activities

TODO

## 🔗 Links

### Cloc Links

#### For Everyone

-   **<https://app.cloc.ai>** - Ever® Cloc™ Platform Web Application.
-   **<https://demo.cloc.ai>** - Ever® Cloc™ Platform Demo Web Application.
-   **<https://stage.cloc.ai>** - Ever® Cloc™ Platform Stage / Test Web Application.
-   **<https://docs.cloc.ai>** - Ever® Cloc™ Platform Documentation (WIP).
-   **<https://cloc.ai>** - Check more information about the Ever® Cloc™ Platform at the official website (WIP).

#### For Developers

##### Main Links

-   **<https://storybook.cloc.ai>** - Ever® Cloc™ Platform Kit Storybook.
-   **<https://builders.cloc.ai>** - Ever® Cloc™ Platform Visual Builders.

##### Examples

- **<https://base.examples.cloc.ai>** - Ever® Cloc™ base example
- **<https://next.examples.cloc.ai>** - Ever® Cloc™ NextJs example
- **<https://saas.examples.cloc.ai>** - Ever® Cloc™ SaaS platform example
- **<https://vite.examples.cloc.ai>** - Ever® Cloc™ example built with Vite
- **<https://next-boilerplate.examples.cloc.ai>** - Ever® Cloc™ example built with NextJs Boilerplate
- **<https://remix.examples.cloc.ai>** -  Ever® Cloc™ example built with Remix

### Other Links

-   **<https://ever.team>** - Ever® Teams™ - Open Work and Project Management Platform.
-   **<https://gauzy.co>** - Ever® Gauzy™ - Open Business Management Platform (ERP/CRM/HRM/ATS/PM).
-   **<https://ever.co>** - Get more information about our company products.

## 🧱 Technology Stack and Requirements

### Common

Below is a list of frameworks/libraries we are using across platform apps:

-   [Turborepo](https://turbo.build/repo)
-   [GitHub Actions](https://docs.github.com/en/actions)
-   [TypeScript](https://www.typescriptlang.org)
-   [NodeJs](https://nodejs.org)
-   [Next.js](https://nextjs.org)
-   [React](https://reactjs.org)
-   [React Native](https://reactnative.dev)
-   [Expo](https://expo.dev)
-   [Tailwind](https://tailwindcss.com) - CSS / Styles
-   [shadcn/ui](https://ui.shadcn.com), [Radix](https://www.radix-ui.com) and [HeadlessUI](https://headlessui.com) - UI Component Libraries
-   [Storybook](https://github.com/storybookjs/storybook) - frontend workshop for building UI components and pages in isolation.
-   [DigitalOcean](https://www.digitalocean.com) and [Vercel](https://vercel.com) - Hosting

#### See also README.md and CREDITS.md files in relevant folders for lists of libraries and software included in the Platform, information about licenses, and other details

### Web UI

See [Ever Teams UI Stack](https://github.com/ever-co/ever-teams?tab=readme-ov-file#-technology-stack-and-requirements).

### Desktop App and Servers

See [Ever Gauzy Desktop Apps / Servers Stack](https://github.com/ever-co/ever-gauzy?tab=readme-ov-file#server--desktop-apps).

### APIs

See [Ever Gauzy API Stack](https://github.com/ever-co/ever-gauzy?tab=readme-ov-file#-technology-stack-and-requirements).

### Mobile App

See [Ever Teams Mobile App](https://github.com/ever-co/ever-teams/tree/develop/apps/mobile)

### Toolkit

- [Next.js](https://github.com/vercel/next.js)
- [Headless UI](https://github.com/tailwindlabs/headlessui)
- [Radix UI](https://github.com/radix-ui), e.g. [Primitives](https://github.com/radix-ui/primitives), [Themes](https://github.com/radix-ui/themes), etc.
- [Shadcn UI](https://ui.shadcn.com)
- [TailwindCSS](https://github.com/tailwindlabs/tailwindcss)
- [Storybook](https://github.com/storybookjs/storybook) - frontend workshop for building UI components and pages in isolation.

## 📄 Documentation

Please refer to our official [Platform Documentation](https://docs.cloc.ai) and our [Wiki](https://github.com/ever-co/ever-cloc/wiki).

## 🚀 Getting Starting

### Super Quick Start

<https://app.cloc.ai>

### ✨ Run

1. Clone this mono-repo
2. `yarn install`
3. `yarn run dev`
4. Go to `http:localhost:3000` to see examples

## 📄 Content

TODO

## 💌 Contact Us

-   [Ever.co Website Contact Us page](https://ever.co/contacts)
-   [Slack Community](https://join.slack.com/t/gauzy/shared_invite/enQtNzc5MTA5MDUwODg2LTI0MGEwYTlmNWFlNzQzMzBlOWExNTk0NzAyY2IwYWYwMzZjMTliYjMwNDI3NTJmYmM4MDQ4NDliMDNiNDY1NWU)
-   [Discord Chat](https://discord.gg/hKQfn4j)
-   [![Gitter](https://badges.gitter.im/JoinChat.svg)](https://gitter.im/cloc-co/ever-cloc?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)
-   [![Get help on Codementor](https://cdn.codementor.io/badges/get_help_github.svg)](https://www.codementor.io/evereq?utm_source=github&utm_medium=button&utm_term=evereq&utm_campaign=github)
-   For business inquiries: <mailto:<EMAIL>>
-   Please report security vulnerabilities to <mailto:<EMAIL>>
-   [Cloc Platform @ X/Twitter](https://x.com/cloc_ai)
-   [Cloc Platform @ Facebook](https://www.facebook.com/clocai)

## 🔐 Security

**Ever Cloc Platform** follows good security practices, but 100% security cannot be guaranteed in any software!
**Ever Cloc Platform** is provided AS IS without any warranty. Use at your own risk!
See more details in the [LICENSES.md](LICENSES.md).

In a production setup, all client-side to server-side (backend, APIs) communications should be encrypted using HTTPS/WSS/SSL (REST APIs, GraphQL endpoint, Socket.io WebSockets, etc.).

If you discover any issue regarding security, please disclose the information responsibly by emailing <mailto:<EMAIL>> and not by creating a GitHub issue.

## 🛡️ License

This software is available under the following licenses:

-   [Ever® Cloc™ Platform Community Edition](https://github.com/cloc-co/ever-cloc/blob/master/LICENSES.md#ever-cloc-platform-community-edition-license)
-   [Ever® Cloc™ Platform Small Business](https://github.com/cloc-co/ever-cloc/blob/master/LICENSES.md#ever-cloc-platform-small-business-license)
-   [Ever® Cloc™ Platform Enterprise](https://github.com/cloc-co/ever-cloc/blob/master/LICENSES.md#ever-cloc-platform-enterprise-license)

#### The default Ever® Cloc™ Platform license, without a valid Ever® Cloc™ Platform Enterprise or Ever® Cloc™ Platform Small Business License agreement, is the Ever® Cloc™ Platform Community Edition License

We support the open-source community. If you're building awesome non-profit/open-source projects, we're happy to help and will provide (subject to [acceptance criteria](https://github.com/cloc-co/ever-cloc/wiki/Free-license-and-hosting-for-Non-profit-and-Open-Source-projects)) Ever Cloc Enterprise edition license and free hosting option! Feel free to contact us at <mailto:<EMAIL>> to make a request. More details are explained in our [Wiki](https://github.com/cloc-co/ever-cloc/wiki/Free-license-and-hosting-for-Non-profit-and-Open-Source-projects).

#### Please see [LICENSES](LICENSES.md) for more information on licenses

[![FOSSA Status](https://app.fossa.io/api/projects/git%2Bgithub.com%2Fcloc-co%2Fever-cloc.svg?type=large)](https://app.fossa.io/projects/git%2Bgithub.com%2Fcloc-co%2Fever-cloc?ref=badge_large)

## ™️ Trademarks

**Ever**® is a registered trademark of [Ever Co. LTD](https://ever.co).
**Ever® Cloc™**, **Ever® Demand™**, **Ever® Gauzy™**, **Ever® Teams™**, **Ever® Rec™** and **Ever® OpenSaaS™** are all trademarks of [Ever Co. LTD](https://ever.co).

The trademarks may only be used with the written permission of Ever Co. LTD. and may not be used to promote or otherwise market competitive products or services.

All other brand and product names are trademarks, registered trademarks, or service marks of their respective holders.

## 🍺 Contribute

-   Please give us a :star: on Github, it **helps**!
-   You are more than welcome to submit feature requests in the [separate repo](https://github.com/ever-co/feature-requests/issues)
-   Pull requests are always welcome! Please base pull requests against the _develop_ branch and follow the [contributing guide](.github/CONTRIBUTING.md).

## 💪 Thanks to our Contributors

See our contributors list in [CONTRIBUTORS.md](https://github.com/cloc-co/ever-cloc/blob/develop/.github/CONTRIBUTORS.md).
You can also view a full list of our [contributors tracked by Github](https://github.com/cloc-co/ever-cloc/graphs/contributors).

<img src="https://contributors-img.web.app/image?repo=cloc-co/ever-cloc" />

## ⭐ Star History

[![Star History Chart](https://api.star-history.com/svg?repos=cloc-co/ever-cloc&type=Date)](https://star-history.com/#cloc-co/ever-cloc&Date)

## ❤️ Powered By

<p>
  <a href="https://www.digitalocean.com/?utm_medium=opensource&utm_source=ever-co">
    <img src="https://opensource.nyc3.cdn.digitaloceanspaces.com/attribution/assets/PoweredByDO/DO_Powered_by_Badge_blue.svg" width="201px">
  </a>
</p>

<p>
 <a href="https://vercel.com/?utm_source=ever-co&utm_campaign=oss">
     <img src=".github/vercel-logo.svg" alt="Powered by Vercel" />
 </a>
</p>

## ©️ Copyright

#### Copyright © 2024-present, Ever Co. LTD. All rights reserved

---

[![Circle CI](https://circleci.com/gh/cloc-co/ever-cloc.svg?style=svg)](https://circleci.com/gh/cloc-co/ever-cloc)
[![codecov](https://codecov.io/gh/cloc-co/ever-cloc/branch/master/graph/badge.svg)](https://codecov.io/gh/cloc-co/ever-cloc)
[![Codacy Badge](https://app.codacy.com/project/badge/Grade/8c46f9eb9df64aa9859dea4d572059ac)](https://www.codacy.com/gh/cloc-co/ever-cloc/dashboard?utm_source=github.com&utm_medium=referral&utm_content=cloc-co/ever-cloc&utm_campaign=Badge_Grade)
[![DeepScan grade](https://deepscan.io/api/teams/3293/projects/25855/branches/814579/badge/grade.svg)](https://deepscan.io/dashboard#view=project&tid=3293&pid=25855&bid=814579)
[![Known Vulnerabilities](https://snyk.io/test/github/cloc-co/ever-cloc/badge.svg)](https://snyk.io/test/github/cloc-co/ever-cloc)
[![Total alerts](https://img.shields.io/lgtm/alerts/g/cloc-co/ever-cloc.svg?logo=lgtm&logoWidth=18)](https://lgtm.com/projects/g/cloc-co/ever-cloc/alerts/)
[![Language grade: JavaScript](https://img.shields.io/lgtm/grade/javascript/g/cloc-co/ever-cloc.svg?logo=lgtm&logoWidth=18)](https://lgtm.com/projects/g/cloc-co/ever-cloc/context:javascript)
[![FOSSA Status](https://app.fossa.io/api/projects/git%2Bgithub.com%2Fcloc-co%2Fever-cloc.svg?type=shield)](https://app.fossa.io/projects/git%2Bgithub.com%2Fcloc-co%2Fever-cloc?ref=badge_shield)
[![Crowdin](https://badges.crowdin.net/e/1d2b3405d65a56ec116d0984fd579cc9/localized.svg)](https://ever.crowdin.com/ever-cloc)

## 🔥 P.S

-   If you are running any business or doing freelance, check our new project [Ever Gauzy](https://github.com/ever-co/ever-gauzy) - Open Business Management Platform (ERP/CRM/HRM)
-   [We are Hiring: remote TypeScript / NodeJS / NestJS / Angular & React developers](https://github.com/ever-co/jobs#available-positions)
