# Cloc Mobile App

Ever Cloc Mobile Apps (iOS / Android) are built with ReactNative / Expo using [Ignite Boilerplate](https://github.com/infinitered/ignite).
The Source code for the Ever Cloc Mobile App is located at <https://github.com/ever-co/ever-teams/tree/develop/apps/mobile>

- Google Android App available at: <https://play.google.com/store/apps/details?id=ever.team>
- Apple iOS App available at: ...

## License and Trademark Notice

The mobile application's source code is licensed under the [GNU General Public License (GPL) v3.0](https://www.gnu.org/licenses/gpl-3.0.txt).

The app’s name, logo, and other associated brand assets, including those related to **Ever Cloc** and **[Ever Co. LTD.](https://ever.co)**, are the exclusive property of **Ever Co. LTD.** These trademarks and brand elements are *not* covered by the GPL license and may not be used, modified, or redistributed without the **explicit written permission** of Ever Co. LTD.

Please note that some files may be automatically added or installed during the build process (for example, through npm dependencies). Such third-party components are governed by their **own respective licenses**, which may impose additional or more restrictive terms. It is your responsibility to review and comply with the license terms applicable to any third-party dependencies included in this project.
