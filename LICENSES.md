# License

Copyright © 2024-present, Ever Co. LTD. All rights reserved.

This document represents official information about our licensing. Make sure you read and understand it before starting to use the software and source code.

- Check more detailed information about licensing in our [Wiki](https://github.com/cloc-co/ever-cloc/wiki/Licensing).
- You can also ask any questions in the [Issue](https://github.com/cloc-co/ever-cloc/issues/TODO) or [Contact Us](https://github.com/cloc-co/ever-cloc#contact-us).

This software is available under an Open Source License ("Community Edition"). It is suitable if your business can comply with the requirements of corresponding open-source licenses, see more information below (e.g. requirements to release your modifications under the same open-source licenses for the benefit of our community).

Alternatively, commercial versions of the Ever Cloc software must be used in accordance with the terms and conditions of the separate written license agreement between you and Ever Co. LTD. With a commercial license, your source code (including your changes) is kept proprietary. You can purchase a commercial license at <https://cloc.ai/pricing>.

In addition, Ever Co. LTD holds copyright and/or sufficient licenses to all components of the Ever Cloc Platform, and therefore can grant, at its sole discretion, the ability for companies, individuals, or organizations to create proprietary modules that may be dynamically linked at runtime with the portions of Ever Cloc Platform which fall under our copyright/license umbrella.

We support the open-source community. If you're building awesome non-profit/open-source projects, we're happy to help and will provide (subject to [acceptance criteria](https://github.com/cloc-co/ever-cloc/wiki/Free-license-and-hosting-for-Non-profit-and-Open-Source-projects)) Ever Cloc Enterprise edition license and free hosting option! Feel free to contact us at <mailto:<EMAIL>> to make a request. More details are explained in our [Wiki](https://github.com/cloc-co/ever-cloc/wiki/Free-license-and-hosting-for-Non-profit-and-Open-Source-projects).

**The default Ever Cloc™ Platform license, without a valid Ever Cloc™ Platform Small Business or Ever Cloc™ Platform Enterprise License agreement, is the Ever Cloc™ Platform Community Edition License.**

### _Ever Cloc Platform Community Edition_ License

Ever Cloc™ Platform Community Edition available at https://github.com/cloc-co/ever-cloc released under [GNU Affero General Public License v3.0](https://www.gnu.org/licenses/agpl-3.0.txt).

If you decide to choose the Ever Cloc™ Platform Community Edition License, you must comply with the following terms:

This program is free software: you can redistribute it and/or modify it under the terms of the corresponding licenses described in the LICENSE / LICENSE.md files located in software sub-folders and under the terms of licenses described in individual files.

This program is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

You should have received a copy of the relevant GNU Licenses along with this program. If not, see <http://www.gnu.org/licenses>.

We suggest checking out a great overview of different open-source licenses at <https://choosealicense.com/licenses>.  
For example, for AGPL v3 (the strongest copyleft license we use), conditions can be summarized as follows:

-   making available complete source code of licensed works and modifications, which include larger works using a licensed work, under the same license.
-   Copyright and license notices must be preserved
-   Contributors provide an express grant of patent rights.
-   When a modified version is used to provide a service over a network, the complete source code of the modified version must be made available.

Please note that some files may be automatically added or installed during the build process (for example, through npm dependencies). Such third-party components are governed by their **own respective licenses**, which may impose additional or more restrictive terms. It is your responsibility to review and comply with the license terms applicable to any third-party dependencies included in this project.

Feel free to [Contact Us](https://github.com/cloc-co/ever-cloc#contact-us) for additional information about the open-source licenses!

### _Ever Cloc Platform Small Business_ License

Ever Cloc™ Platform Small Business License can be purchased by small businesses with annual revenues that do not exceed \$1 million and used for a single-owned Company.
For more information, please see https://cloc.ai/pricing or contact us at <mailto:<EMAIL>>.

### _Ever Cloc Platform Enterprise_ License

Ever Cloc™ Platform Enterprise License can be purchased by businesses with more than \$1 million in annual revenue and used for an unlimited number of owned companies.
For more information, please see https://cloc.ai/pricing or contact us at <mailto:<EMAIL>>.

## Credits

Please see [CREDITS.md](CREDITS.md) files for a list of libraries and software included in this program and information about licenses.

## Trademarks

- **Ever**® is a registered trademark of [Ever Co. LTD](https://ever.co).
- **Ever® Cloc**™, **Ever® Cloc Platform™**, **Ever® Cloc Platform Community™**, **Ever® Cloc Platform Small Business™** and **Ever® Cloc Platform Enterprise™** are all trademarks of [Ever Co. LTD](https://ever.co).
- **Ever® Teams**™ is a trademark of [Ever Co. LTD](https://ever.co).
- **Ever® Gauzy**™ is a trademark of [Ever Co. LTD](https://ever.co).

The trademarks and logos may only be used with the written permission of Ever Co. LTD. and may not be used to promote or otherwise market competitive products or services. If you wish to use these trademarks and logos you should contact our licensing department at <mailto:<EMAIL>> to determine the necessary steps you must take.

All other brand and product names are trademarks, registered trademarks or service marks of their respective holders.

If you have any questions regarding our licensing policy, please contact us: <mailto:<EMAIL>> (via email).
