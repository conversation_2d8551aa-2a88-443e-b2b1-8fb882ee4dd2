import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { ClocProgress } from '@cloc/atoms';

const meta = {
	title: 'Utilities/Display/Progress',
	component: ClocProgress,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component:
					"A progress bar component that automatically calculates progress based on today's tracked time from the Cloc context. Shows progress towards an 8-hour daily goal."
			}
		}
	},
	argTypes: {
		className: {
			control: 'text',
			description: 'Additional CSS classes to apply to the progress bar'
		}
	}
} satisfies Meta<typeof ClocProgress>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		className: 'w-64'
	}
};

export const FullWidth: Story = {
	args: {
		className: 'w-full'
	}
};

export const Small: Story = {
	args: {
		className: 'w-32 h-2'
	}
};

export const Large: Story = {
	args: {
		className: 'w-96 h-4'
	}
};

export const CustomStyling: Story = {
	args: {
		className: 'w-64 h-3 rounded-full'
	}
};

export const WithLabel: Story = {
	render: (args) => (
		<div className="space-y-2">
			<div className="flex justify-between text-sm text-gray-600">
				<span>Daily Progress</span>
				<span>Today's tracked time</span>
			</div>
			<ClocProgress {...args} />
		</div>
	),
	args: {
		className: 'w-80'
	}
};

export const ProgressCard: Story = {
	render: (args) => (
		<div className="p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm space-y-3">
			<h3 className="font-semibold text-gray-900 dark:text-white">Today's Work Progress</h3>
			<div className="space-y-2">
				<div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
					<span>Progress towards 8-hour goal</span>
					<span>Auto-calculated</span>
				</div>
				<ClocProgress {...args} />
				<p className="text-xs text-gray-500 dark:text-gray-400">
					Progress is automatically calculated based on your tracked time today
				</p>
			</div>
		</div>
	),
	args: {
		className: 'w-full'
	}
};

export const MultipleProgress: Story = {
	render: () => (
		<div className="space-y-4 w-80">
			<div className="space-y-2">
				<label className="text-sm font-medium">Daily Goal (8 hours)</label>
				<ClocProgress className="w-full" />
			</div>
			<div className="space-y-2">
				<label className="text-sm font-medium">Weekly Goal (40 hours)</label>
				<ClocProgress className="w-full h-2" />
			</div>
			<div className="space-y-2">
				<label className="text-sm font-medium">Monthly Goal (160 hours)</label>
				<ClocProgress className="w-full h-1" />
			</div>
		</div>
	)
};

export const DifferentSizes: Story = {
	render: () => (
		<div className="space-y-4">
			<div className="space-y-2">
				<label className="text-sm">Extra Small (h-1)</label>
				<ClocProgress className="w-64 h-1" />
			</div>
			<div className="space-y-2">
				<label className="text-sm">Small (h-2)</label>
				<ClocProgress className="w-64 h-2" />
			</div>
			<div className="space-y-2">
				<label className="text-sm">Default (h-3)</label>
				<ClocProgress className="w-64" />
			</div>
			<div className="space-y-2">
				<label className="text-sm">Large (h-4)</label>
				<ClocProgress className="w-64 h-4" />
			</div>
			<div className="space-y-2">
				<label className="text-sm">Extra Large (h-6)</label>
				<ClocProgress className="w-64 h-6" />
			</div>
		</div>
	)
};
